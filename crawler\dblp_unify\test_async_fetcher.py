#!/usr/bin/env python3
"""
异步摘要获取器测试脚本
用于测试AsyncAbstractFetcher类的功能
"""

import os
import sys
import json
import asyncio
import logging
import time
from pathlib import Path

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_DIR)

from crawler.dblp_unify.crawler_dblp_unify import AsyncAbstractFetcher
from crawler.dblp_unify.async_config import get_config, print_config_info


def create_test_data(test_dir: str):
    """创建测试数据"""
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建一个包含少量论文的测试JSON文件
    test_papers = {
        'metadata': {
            'venue_name': 'test_venue',
            'year': 2023,
            'total_papers': 3,
            'fetch_time': '2023-01-01T00:00:00',
            'source': 'Test Data'
        },
        'papers': [
            {
                'title': 'Test Paper 1',
                'doi': '10.1145/3123456.3123457',  # 这个DOI可能不存在，用于测试失败情况
                'authors': ['Author 1', 'Author 2']
            },
            {
                'title': 'Test Paper 2',
                'doi': '10.1038/nature12373',  # 这是一个真实的DOI，用于测试成功情况
                'authors': ['Author 3', 'Author 4']
            },
            {
                'title': 'Test Paper 3 - No DOI',
                'authors': ['Author 5', 'Author 6']
                # 没有DOI，用于测试跳过情况
            }
        ]
    }
    
    test_file = os.path.join(test_dir, 'test_venue_2023.json')
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_papers, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试数据: {test_file}")
    return test_file


async def test_async_fetcher():
    """测试异步摘要获取器"""
    
    # 1. 创建测试目录和数据
    test_dir = os.path.join(ROOT_DIR, 'data', 'test_async')
    test_file = create_test_data(test_dir)
    
    # 2. 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    
    # 3. 显示配置信息
    print_config_info()
    
    # 4. 测试不同的配置预设
    presets_to_test = ['test', 'conservative']
    
    for preset in presets_to_test:
        print(f"\n🧪 测试配置预设: {preset}")
        print("=" * 50)
        
        config = get_config(preset)
        fetcher = AsyncAbstractFetcher(
            max_concurrent=config['max_concurrent'],
            delay_seconds=config['delay_seconds']
        )
        
        start_time = time.time()
        await fetcher.fetch_abstracts_for_directory(test_dir)
        end_time = time.time()
        
        print(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
        
        # 检查结果
        with open(test_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        papers_with_abstract = sum(1 for p in result_data['papers'] if 'abstract' in p)
        print(f"📊 结果: {papers_with_abstract}/{len(result_data['papers'])} 篇论文获得了摘要")
    
    # 5. 清理测试数据
    print(f"\n🧹 清理测试数据: {test_dir}")
    import shutil
    shutil.rmtree(test_dir)


def test_sync_wrapper():
    """测试同步包装器"""
    from crawler.dblp_unify.crawler_dblp_unify import main_papers_abstract
    
    # 创建测试数据
    test_dir = os.path.join(ROOT_DIR, 'data', 'test_sync')
    create_test_data(test_dir)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    
    print("\n🧪 测试同步包装器")
    print("=" * 50)
    
    # 使用预设配置
    start_time = time.time()
    main_papers_abstract(test_dir, preset='test')
    end_time = time.time()
    
    print(f"⏱️ 同步包装器耗时: {end_time - start_time:.2f} 秒")
    
    # 清理测试数据
    import shutil
    shutil.rmtree(test_dir)


def main():
    """主测试函数"""
    print("🚀 开始测试异步摘要获取器")
    print("=" * 60)
    
    # 测试异步版本
    asyncio.run(test_async_fetcher())
    
    # 测试同步包装器
    test_sync_wrapper()
    
    print("\n✅ 所有测试完成!")


if __name__ == "__main__":
    main()
