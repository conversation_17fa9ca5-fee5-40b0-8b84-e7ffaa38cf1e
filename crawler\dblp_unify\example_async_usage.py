#!/usr/bin/env python3
"""
异步摘要获取器使用示例
演示如何使用AsyncAbstractFetcher类进行并发摘要获取
"""

import os
import sys
import asyncio
import logging
import time
from pathlib import Path

# 添加项目根目录到路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(ROOT_DIR)

from crawler.dblp_unify.crawler_dblp_unify import AsyncAbstractFetcher


async def example_usage():
    """异步摘要获取使用示例"""
    
    # 1. 设置数据目录
    data_dir = os.path.join(ROOT_DIR, 'data', 'paper', 'conf_a')
    
    # 2. 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
        ]
    )
    
    # 3. 创建异步摘要获取器
    # 参数说明:
    # - max_concurrent: 最大并发数，建议5-20之间
    #   - 太小: 速度慢
    #   - 太大: 可能被API限流或网络拥塞
    # - delay_seconds: 请求间延迟，建议0.1-1.0秒
    #   - 太小: 可能被限流
    #   - 太大: 速度慢
    
    fetcher = AsyncAbstractFetcher(
        max_concurrent=10,    # 最大并发数
        delay_seconds=0.1     # 请求间延迟
    )
    
    # 4. 开始获取摘要
    print("🚀 开始异步获取论文摘要...")
    start_time = time.time()
    
    await fetcher.fetch_abstracts_for_directory(data_dir)
    
    end_time = time.time()
    print(f"⏱️ 总耗时: {end_time - start_time:.2f} 秒")


def main():
    """主函数 - 同步包装器"""
    asyncio.run(example_usage())


if __name__ == "__main__":
    main()
