#!/usr/bin/env python3
"""
异步摘要获取器配置文件
用于集中管理并发参数和其他配置
"""

# 并发控制配置
ASYNC_CONFIG = {
    # 基础配置
    'max_concurrent': 10,        # 最大并发数 (建议: 5-20)
    'delay_seconds': 0.1,        # 请求间延迟秒数 (建议: 0.1-1.0)
    
    # 网络配置
    'timeout_seconds': 30,       # 请求超时时间
    'max_retries': 5,           # 最大重试次数
    'retry_delays': [1, 3, 5, 10, 20],  # 重试延迟序列(秒)
    
    # 性能配置
    'connector_limit': 100,      # TCP连接池大小
    'batch_size': 50,           # 批处理大小
    
    # 日志配置
    'log_level': 'INFO',        # 日志级别
    'log_progress': True,       # 是否显示进度条
}

# 不同场景的预设配置
PRESET_CONFIGS = {
    # 保守模式 - 适合网络不稳定或API限制严格的情况
    'conservative': {
        'max_concurrent': 5,
        'delay_seconds': 0.5,
        'timeout_seconds': 60,
        'max_retries': 3,
        'retry_delays': [2, 5, 10],
    },
    
    # 标准模式 - 平衡速度和稳定性
    'standard': {
        'max_concurrent': 10,
        'delay_seconds': 0.1,
        'timeout_seconds': 30,
        'max_retries': 5,
        'retry_delays': [1, 3, 5, 10, 20],
    },
    
    # 激进模式 - 追求最大速度，适合网络条件好的情况
    'aggressive': {
        'max_concurrent': 20,
        'delay_seconds': 0.05,
        'timeout_seconds': 15,
        'max_retries': 3,
        'retry_delays': [0.5, 1, 2],
    },
    
    # 测试模式 - 用于小规模测试
    'test': {
        'max_concurrent': 3,
        'delay_seconds': 1.0,
        'timeout_seconds': 10,
        'max_retries': 2,
        'retry_delays': [1, 3],
    }
}


def get_config(preset: str = 'standard') -> dict:
    """
    获取配置
    
    Args:
        preset: 预设配置名称 ('conservative', 'standard', 'aggressive', 'test')
        
    Returns:
        配置字典
    """
    if preset in PRESET_CONFIGS:
        config = ASYNC_CONFIG.copy()
        config.update(PRESET_CONFIGS[preset])
        return config
    else:
        return ASYNC_CONFIG.copy()


def print_config_info():
    """打印配置信息说明"""
    print("🔧 异步摘要获取器配置说明:")
    print("=" * 60)
    
    print("\n📋 可用预设配置:")
    for preset, config in PRESET_CONFIGS.items():
        print(f"  • {preset:12} - 并发:{config['max_concurrent']:2d}, 延迟:{config['delay_seconds']:4.2f}s")
    
    print("\n⚙️ 参数说明:")
    print("  • max_concurrent: 最大并发数")
    print("    - 太小: 速度慢")
    print("    - 太大: 可能被API限流")
    print("    - 建议: 5-20")
    
    print("  • delay_seconds: 请求间延迟")
    print("    - 太小: 可能被限流")
    print("    - 太大: 速度慢")
    print("    - 建议: 0.1-1.0秒")
    
    print("  • timeout_seconds: 单个请求超时时间")
    print("  • max_retries: 失败重试次数")
    print("  • retry_delays: 重试延迟序列")
    
    print("\n💡 使用建议:")
    print("  • 网络不稳定 → 使用 'conservative' 预设")
    print("  • 一般情况   → 使用 'standard' 预设")
    print("  • 网络很好   → 使用 'aggressive' 预设")
    print("  • 小规模测试 → 使用 'test' 预设")


if __name__ == "__main__":
    print_config_info()
